"use client";

import React, { useEffect } from "react";
import { AuthProvider } from "@/lib/context/auth-context";
import TokenInjector from "@/components/token-injector";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This effect runs on the client side and adds the token from localStorage
  // to request headers before they are sent to the server
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Override fetch to inject the auth token
      const originalFetch = window.fetch;
      window.fetch = async function(input, init) {
        init = init || {};
        init.headers = init.headers || {};
        
        // Add Authorization header to the request if token exists
        const token = localStorage.getItem('auth_token');
        if (token) {
          const headers = new Headers(init.headers);
          headers.set('Authorization', `Bearer ${token}`);
          init.headers = headers;
        }
        
        return originalFetch(input, init);
      };
      
      // Add a listener for navigation events to set a special header that will be read by middleware
      const handleBeforeUnload = () => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          // We can't directly set headers on navigation, but we'll use localStorage
          // which will be checked by the Next.js middleware
          sessionStorage.setItem('navigation_in_progress', 'true');
        }
      };
      
      window.addEventListener('beforeunload', handleBeforeUnload);
      
      return () => {
        // Restore original fetch when the component is unmounted
        window.fetch = originalFetch;
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, []);

  return (
    <AuthProvider>
      <TokenInjector />
      {children}
    </AuthProvider>
  );
}

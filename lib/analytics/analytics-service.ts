// Simple analytics service to track user behavior and application performance

interface PageViewEvent {
  page: string
  timestamp: number
  userId?: string
  sessionId: string
}

interface UserEvent {
  type: string
  page: string
  element?: string
  details?: Record<string, any>
  timestamp: number
  userId?: string
  sessionId: string
}

interface ErrorEvent {
  message: string
  stack?: string
  page: string
  timestamp: number
  userId?: string
  sessionId: string
}

class AnalyticsService {
  private sessionId: string
  private userId?: string
  private events: (PageViewEvent | UserEvent | ErrorEvent)[] = []
  private isEnabled = true
  private flushInterval: NodeJS.Timeout | null = null
  private flushIntervalMs = 30000 // 30 seconds

  constructor() {
    this.sessionId = this.generateSessionId()
    this.startFlushInterval()
  }

  // Initialize the analytics service
  public init(userId?: string): void {
    this.userId = userId
    console.log(`Analytics initialized. Session ID: ${this.sessionId}, User ID: ${userId || "anonymous"}`)
  }

  // Track page view
  public trackPageView(page: string): void {
    if (!this.isEnabled) return

    const event: PageViewEvent = {
      page,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
    }

    this.events.push(event)
    console.log(`Page view tracked: ${page}`)
  }

  // Track user event (click, form submission, etc.)
  public trackEvent(type: string, page: string, element?: string, details?: Record<string, any>): void {
    if (!this.isEnabled) return

    const event: UserEvent = {
      type,
      page,
      element,
      details,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
    }

    this.events.push(event)
    console.log(`User event tracked: ${type} on ${page}`)
  }

  // Track error
  public trackError(message: string, stack?: string): void {
    if (!this.isEnabled) return

    const event: ErrorEvent = {
      message,
      stack,
      page: typeof window !== "undefined" ? window.location.pathname : "unknown",
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
    }

    this.events.push(event)
    console.log(`Error tracked: ${message}`)
  }

  // Enable or disable analytics
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    console.log(`Analytics ${enabled ? "enabled" : "disabled"}`)

    if (enabled && !this.flushInterval) {
      this.startFlushInterval()
    } else if (!enabled && this.flushInterval) {
      this.stopFlushInterval()
    }
  }

  // Flush events to server
  public async flush(): Promise<void> {
    if (this.events.length === 0) return

    try {
      // In a real application, this would send the events to a server
      console.log(`Flushing ${this.events.length} analytics events to server`)

      // Mock API call
      // await fetch('/api/analytics', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ events: this.events })
      // });

      // Clear events after successful flush
      this.events = []
    } catch (error) {
      console.error("Failed to flush analytics events:", error)
    }
  }

  // Generate a unique session ID
  private generateSessionId(): string {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0
      const v = c === "x" ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }

  // Start the flush interval
  private startFlushInterval(): void {
    this.flushInterval = setInterval(() => {
      this.flush()
    }, this.flushIntervalMs)
  }

  // Stop the flush interval
  private stopFlushInterval(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval)
      this.flushInterval = null
    }
  }
}

// Create a singleton instance
export const analyticsService = new AnalyticsService()

export default analyticsService

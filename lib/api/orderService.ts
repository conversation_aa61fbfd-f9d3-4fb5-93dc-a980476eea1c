import apiClient, { ApiResponse } from './apiClient';

// Types
export type OrderStatus = 'PENDING' | 'APPROVED' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
export type PaymentStatus = 'PENDING' | 'PAID' | 'FAILED';

export interface OrderItem {
  id: string;
  stockId: string;
  stockName: string;
  gsm: number;
  size: string;
  quantity: number;
  price: number;
  subtotal: number;
}

export interface ShippingAddress {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface User {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  contactNumber?: string;
}

export interface Order {
  id: string;
  userId: string;
  companyName: string;
  contactPerson: string;
  contactNumber: string;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingAddress: ShippingAddress;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  user?: User; // User information from the backend
}

export interface OrderQueryParams {
  page?: number;
  limit?: number;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface UpdateOrderStatusDto {
  status: OrderStatus;
  notes?: string;
}

export interface UpdatePaymentStatusDto {
  paymentStatus: PaymentStatus;
  notes?: string;
}

/**
 * Order Service
 */
const orderService = {
  /**
   * Get all orders with pagination and filtering
   */
  async getAllOrders(params: OrderQueryParams = {}): Promise<ApiResponse<{
    orders: Order[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  }>> {
    return await apiClient.get<{
      orders: Order[];
      pagination: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
      };
    }>('/admin/orders', params);
  },

  /**
   * Get an order by ID
   */
  async getOrder(id: string): Promise<Order> {
    const response = await apiClient.get<{ order: Order }>(`/admin/orders/${id}`);
    return response.data.order;
  },

  /**
   * Update order status
   */
  async updateOrderStatus(id: string, data: UpdateOrderStatusDto): Promise<Order> {
    const response = await apiClient.patch<{ order: Order }>(`/admin/orders/${id}/status`, data);
    return response.data.order;
  },

  /**
   * Update payment status
   */
  async updatePaymentStatus(id: string, data: UpdatePaymentStatusDto): Promise<Order> {
    const response = await apiClient.patch<{ order: Order }>(`/admin/orders/${id}/payment-status`, data);
    return response.data.order;
  },
};

export default orderService;

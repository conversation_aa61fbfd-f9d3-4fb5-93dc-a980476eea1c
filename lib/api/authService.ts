import apiClient, { ApiResponse } from './apiClient';

// Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  role: string;
  companyName?: string;
  contactPerson?: string;
  contactNumber?: string;
  gstNumber?: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

/**
 * Authentication Service
 */
const authService = {
  /**
   * Login with email and password
   */
  async login(credentials: LoginCredentials): Promise<User> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);

    // Store the token
    apiClient.setToken(response.data.token);

    // Store user in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(response.data.user));

      // Also set the token in a cookie for middleware
      const cookieExpiry = 60 * 60 * 24 * 7; // 7 days
      document.cookie = `auth_token=${response.data.token}; path=/; max-age=${cookieExpiry}; SameSite=Lax`;
      console.log('Auth token cookie set:', response.data.token.substring(0, 10) + '...');
    }

    return response.data.user;
  },

  /**
   * Logout the current user
   */
  async logout(): Promise<void> {
    console.log('Logging out user');
    // Clear token and user data
    apiClient.clearToken();

    if (typeof window !== 'undefined') {
      localStorage.removeItem('user');

      // Also clear the auth cookie
      document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      console.log('Auth token cookie cleared');
    }
  },

  /**
   * Get the current authenticated user
   */
  getCurrentUser(): User | null {
    if (typeof window !== 'undefined') {
      const userJson = localStorage.getItem('user');
      if (userJson) {
        try {
          return JSON.parse(userJson) as User;
        } catch (error) {
          console.error('Error parsing user data:', error);
        }
      }
    }
    return null;
  },

  /**
   * Check if the current user is authenticated
   */
  isAuthenticated(): boolean {
    return apiClient.isAuthenticated() && !!this.getCurrentUser();
  },

  /**
   * Check if the current user is an admin
   */
  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return !!user && user.role === 'ADMIN';
  },

  /**
   * Check if the auth token cookie is set
   */
  checkAuthCookie(): boolean {
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';');
      const authCookie = cookies.find(cookie => cookie.trim().startsWith('auth_token='));
      console.log('Auth cookie check:', !!authCookie);
      return !!authCookie;
    }
    return false;
  },
};

export default authService;

import apiClient from './apiClient';
import {
  ApiResponse,
  User,
  UserQueryParams,
  RejectUserDto,
  PaginatedResponse
} from '@/lib/types';

/**
 * User Service
 */
const userService = {
  /**
   * Get all users with pagination and filtering
   */
  async getAllUsers(params: UserQueryParams = {}): Promise<ApiResponse<PaginatedResponse<{ users: User[] }>>> {
    return await apiClient.get<PaginatedResponse<{ users: User[] }>>('/admin/users', params);
  },

  /**
   * Get a user by ID
   */
  async getUser(id: string): Promise<ApiResponse<{ user: User }>> {
    return await apiClient.get<{ user: User }>(`/admin/users/${id}`);
  },

  /**
   * Approve a user
   */
  async approveUser(id: string): Promise<ApiResponse<{ user: User }>> {
    return await apiClient.patch<{ user: User }>(`/admin/users/${id}/approve`, {});
  },

  /**
   * Reject a user
   */
  async rejectUser(id: string, data: RejectUserDto): Promise<ApiResponse<{ user: User }>> {
    return await apiClient.patch<{ user: User }>(`/admin/users/${id}/reject`, data);
  },

  /**
   * Get user profile
   */
  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    return await apiClient.get<{ user: User }>('/users/profile');
  },

  /**
   * Update user profile
   */
  async updateProfile(data: { contactPerson?: string; contactNumber?: string; email?: string }): Promise<ApiResponse<{ user: User }>> {
    return await apiClient.patch<{ user: User }>('/users/profile', data);
  },

  /**
   * Change user password
   */
  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse<{ message: string }>> {
    return await apiClient.patch<{ message: string }>('/users/password', data);
  },
};

export default userService;

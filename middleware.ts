import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define public paths that don't require authentication
const publicPaths = ['/login'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the path is public
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path));

  // Get the authentication token from cookies
  const token = request.cookies.get('auth_token')?.value;

  // If the path is not public and there's no token, redirect to login
  if (!isPublicPath && !token) {
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }

  // If the path is login and there's a token, redirect to dashboard
  if (pathname === '/login' && token) {
    const url = new URL('/dashboard', request.url);
    return NextResponse.redirect(url);
  }

  // Allow access to all other paths
  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public directory)
     * - api routes
     * - _vercel system paths
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$|api|_vercel).*)',
  ],
};

"use client";

import React, { useEffect } from 'react';

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  // This effect runs on the client side and adds the token from localStorage
  // to request headers before they are sent to the server
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const originalFetch = window.fetch;
      window.fetch = async function(input, init) {
        const token = localStorage.getItem('auth_token');
        if (token) {
          init = init || {};
          init.headers = init.headers || {};
          
          // Add Authorization header to the request
          const headers = new Headers(init.headers);
          headers.set('Authorization', `Bearer ${token}`);
          init.headers = headers;
        }
        
        return originalFetch(input, init);
      };
      
      return () => {
        // Restore original fetch when the component is unmounted
        window.fetch = originalFetch;
      };
    }
  }, []);

  return <>{children}</>;
}

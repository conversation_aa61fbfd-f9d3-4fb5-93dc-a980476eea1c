import apiClient from './apiClient';
import {
  ApiResponse,
  Inquiry,
  InquiryQueryParams,
  RespondToInquiryDto,
  PaginatedResponse
} from '@/lib/types';

/**
 * Inquiry Service
 */
const inquiryService = {
  /**
   * Get all inquiries with pagination and filtering
   */
  async getAllInquiries(params: InquiryQueryParams = {}): Promise<ApiResponse<{ enquiries: Inquiry[], pagination: any }>> {
    return await apiClient.get<{ enquiries: Inquiry[], pagination: any }>('/admin/enquiries', params);
  },

  /**
   * Get an inquiry by ID
   */
  async getInquiry(id: string): Promise<ApiResponse<{ enquiry: Inquiry }>> {
    return await apiClient.get<{ enquiry: Inquiry }>(`/admin/enquiries/${id}`);
  },

  /**
   * Respond to an inquiry
   */
  async respondToInquiry(id: string, data: RespondToInquiryDto): Promise<ApiResponse<{ enquiry: Inquiry }>> {
    return await apiClient.patch<{ enquiry: Inquiry }>(`/admin/enquiries/${id}/respond`, data);
  },

  /**
   * Create a new inquiry
   */
  async createInquiry(data: { subject: string; message: string; contactPreference: string }): Promise<ApiResponse<{ inquiry: Inquiry }>> {
    return await apiClient.post<{ inquiry: Inquiry }>('/enquiries', data);
  },

  /**
   * Close an inquiry
   */
  async closeInquiry(id: string): Promise<ApiResponse<{ inquiry: Inquiry }>> {
    return await apiClient.patch<{ inquiry: Inquiry }>(`/enquiries/${id}/close`, {});
  },

  /**
   * Get user's inquiries
   */
  async getUserInquiries(params: InquiryQueryParams = {}): Promise<ApiResponse<{ enquiries: Inquiry[], pagination: any }>> {
    return await apiClient.get<{ enquiries: Inquiry[], pagination: any }>('/enquiries', params);
  },
};

export default inquiryService;

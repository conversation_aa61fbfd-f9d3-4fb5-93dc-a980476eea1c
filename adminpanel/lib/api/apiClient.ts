import { toast } from "sonner";

// API base URL - will be set from environment variable
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Error types
export interface ApiError {
  status: string;
  message: string;
  errors?: Array<{ field: string; message: string }>;
}

// Response type
export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

/**
 * API Client for making HTTP requests
 */
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Get the stored authentication token
   */
  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token');
    }
    return null;
  }

  /**
   * Set the authentication token
   */
  public setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  /**
   * Clear the authentication token
   */
  public clearToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.getToken();
  }

  /**
   * Create request headers
   */
  private createHeaders(contentType: string = 'application/json'): HeadersInit {
    const headers: HeadersInit = {};

    // Only set Content-Type for non-FormData requests
    // For FormData (multipart/form-data), the browser will set the Content-Type with boundary
    if (contentType !== 'multipart/form-data') {
      headers['Content-Type'] = contentType;
    }

    const token = this.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): never {
    console.error('API Error:', error);

    let errorMessage = 'An unexpected error occurred';

    if (error.status === 401) {
      // Unauthorized - clear token and redirect to login
      this.clearToken();
      errorMessage = 'Your session has expired. Please log in again.';

      // Clear stale user data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user_data');
        
        // Clear token refresh interval if exists
        const intervalId = sessionStorage.getItem('token_refresh_interval');
        if (intervalId) {
          clearInterval(parseInt(intervalId));
          sessionStorage.removeItem('token_refresh_interval');
        }
        
        // Redirect to login page if in browser
        window.location.href = '/login';
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    // Show error toast
    toast.error(errorMessage);

    throw error;
  }

  /**
   * Make a request to the API
   */
  private async request<T>(
    endpoint: string,
    method: string = 'GET',
    data?: any,
    contentType: string = 'application/json'
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = this.createHeaders(contentType);

    const options: RequestInit = {
      method,
      headers,
    };

    if (data) {
      if (contentType === 'application/json') {
        options.body = JSON.stringify(data);
      } else {
        options.body = data;
      }
    }

    try {
      const response = await fetch(url, options);
      const responseData = await response.json();

      if (!response.ok) {
        // Check if this is a 401 error and we can try token refresh
        if (response.status === 401 && typeof window !== 'undefined') {
          try {
            // Try to refresh the token using Firebase if available
            const authModule = await import('@/lib/firebase/auth');
            const firebase = authModule.firebaseAuthService;
            const firebaseUser = await firebase.getCurrentUser();
            
            if (firebaseUser) {
              // We have a Firebase user, try to get a fresh token
              const freshToken = await firebaseUser.getIdToken(true);
              
              // Update token in localStorage
              this.setToken(freshToken);
              
              // Retry the request with the new token
              const retryHeaders = this.createHeaders(contentType);
              const retryOptions: RequestInit = {
                ...options,
                headers: retryHeaders
              };
              
              const retryResponse = await fetch(url, retryOptions);
              const retryData = await retryResponse.json();
              
              if (retryResponse.ok) {
                return retryData as ApiResponse<T>;
              }
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
          }
        }
        
        const error: ApiError = {
          status: responseData.status || 'error',
          message: responseData.message || 'An error occurred',
          errors: responseData.errors,
        };
        return this.handleError(error);
      }

      return responseData as ApiResponse<T>;
    } catch (error) {
      return this.handleError(error as Error);
    }
  }

  /**
   * GET request
   */
  public async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let url = endpoint;

    if (params) {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });

      const queryString = queryParams.toString();
      if (queryString) {
        url = `${endpoint}?${queryString}`;
      }
    }

    return this.request<T>(url, 'GET');
  }

  /**
   * POST request
   */
  public async post<T>(endpoint: string, data: any, contentType: string = 'application/json'): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'POST', data, contentType);
  }

  /**
   * PUT request
   */
  public async put<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PUT', data);
  }

  /**
   * PATCH request
   */
  public async patch<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PATCH', data);
  }

  /**
   * DELETE request
   */
  public async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'DELETE');
  }
}

// Create and export a singleton instance
const apiClient = new ApiClient();
export default apiClient;

import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updatePassword,
  User as FirebaseUser,
  UserCredential,
  onAuthStateChanged,
  getIdToken,
} from 'firebase/auth';
import { auth } from './firebase';

/**
 * Firebase Authentication Service
 */
export const firebaseAuthService = {
  /**
   * Get the current Firebase user
   */
  getCurrentUser(): Promise<FirebaseUser | null> {
    return new Promise((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe();
        resolve(user);
      });
    });
  },

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string): Promise<UserCredential> {
    return signInWithEmailAndPassword(auth, email, password);
  },

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    return signOut(auth);
  },

  /**
   * Get the ID token for the current user
   */
  async getIdToken(): Promise<string | null> {
    const user = auth.currentUser;
    if (!user) return null;
    return user.getIdToken(true);
  },

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string): Promise<void> {
    return sendPasswordResetEmail(auth, email);
  },

  /**
   * Update user password
   */
  async updatePassword(newPassword: string): Promise<void> {
    const user = auth.currentUser;
    if (!user) throw new Error('No user is signed in');
    return updatePassword(user, newPassword);
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!auth.currentUser;
  },
};

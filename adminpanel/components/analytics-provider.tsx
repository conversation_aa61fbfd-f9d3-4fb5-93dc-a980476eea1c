"use client"

import { create<PERSON>ontext, useContext, useEffect, type <PERSON>actNode } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import analyticsService from "@/lib/analytics/analytics-service"
import { useAuth } from "@/lib/context/auth-context"

// Create context
const AnalyticsContext = createContext<{
  trackEvent: (type: string, element?: string, details?: Record<string, any>) => void
  trackError: (message: string, stack?: string) => void
}>({
  trackEvent: () => {},
  trackError: () => {},
})

// Provider component
export function AnalyticsProvider({ children }: { children: ReactNode }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const { user } = useAuth()

  // Initialize analytics with user ID when available
  useEffect(() => {
    if (user) {
      analyticsService.init(user.localId)
    } else {
      analyticsService.init()
    }
  }, [user])

  // Track page views
  useEffect(() => {
    // Track page view when pathname changes
    analyticsService.trackPageView(pathname)
  }, [pathname])

  // Track search params changes
  useEffect(() => {
    if (searchParams && searchParams.toString()) {
      analyticsService.trackEvent("search_params_change", pathname, undefined, {
        params: searchParams.toString(),
      })
    }
  }, [searchParams, pathname])

  // Set up error tracking
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      analyticsService.trackError(event.message || "Unknown error", event.error?.stack)
    }

    // Add global error handler
    window.addEventListener("error", handleError)

    // Clean up
    return () => {
      window.removeEventListener("error", handleError)
    }
  }, [])

  // Flush events on unmount
  useEffect(() => {
    return () => {
      analyticsService.flush()
    }
  }, [])

  // Helper functions for components to use
  const trackEvent = (type: string, element?: string, details?: Record<string, any>) => {
    analyticsService.trackEvent(type, pathname, element, details)
  }

  const trackError = (message: string, stack?: string) => {
    analyticsService.trackError(message, stack)
  }

  return <AnalyticsContext.Provider value={{ trackEvent, trackError }}>{children}</AnalyticsContext.Provider>
}

// Hook for using analytics in components
export function useAnalytics() {
  return useContext(AnalyticsContext)
}

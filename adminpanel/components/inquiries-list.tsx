"use client"

import { useState, useEffect } from "react"
import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, Send } from "lucide-react"
import inquiryService from "@/lib/api/inquiryService"
import { Inquiry, InquiryStatus } from "@/lib/types"
import { format } from "date-fns"
import { toast } from "sonner"
import { Skeleton } from "@/components/ui/skeleton"

export interface InquiriesListProps {
  status?: string;
  onRefresh?: () => void;
}

export function InquiriesList({ status = "all", onRefresh }: InquiriesListProps) {
  const [inquiries, setInquiries] = useState<Inquiry[]>([])
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isReplyMode, setIsReplyMode] = useState(false)
  const [replyText, setReplyText] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch inquiries from the API
  useEffect(() => {
    const fetchInquiries = async () => {
      setIsLoading(true)
      try {
        const params: any = {
          page: 1,
          limit: 20
        }

        // Map the UI status to API status
        if (status !== "all") {
          params.status = status.toUpperCase() as InquiryStatus
        }

        const response = await inquiryService.getAllInquiries(params)

        if (response.data && response.data.enquiries) {
          setInquiries(response.data.enquiries)
        } else {
          console.error("Unexpected response structure:", response)
          toast.error("Failed to load inquiries. Unexpected data format.")
        }
      } catch (error) {
        console.error("Error fetching inquiries:", error)
        toast.error("Failed to load inquiries. Please try again.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchInquiries()
  }, [status])

  // Handle view details
  const handleViewDetails = async (inquiry: Inquiry) => {
    try {
      // Get full inquiry details
      const response = await inquiryService.getInquiry(inquiry.id)
      if (response.data && response.data.enquiry) {
        setSelectedInquiry(response.data.enquiry)
        setIsReplyMode(false)
        setIsDialogOpen(true)
      } else {
        console.error("Unexpected response structure:", response)
        toast.error("Failed to load inquiry details. Unexpected data format.")
      }
    } catch (error) {
      console.error("Error fetching inquiry details:", error)
      toast.error("Failed to load inquiry details. Please try again.")
    }
  }

  // Handle reply mode
  const handleReply = () => {
    setIsReplyMode(true)
    setReplyText("")
  }

  // Handle send reply
  const handleSendReply = async () => {
    if (!selectedInquiry || !replyText.trim()) {
      toast.error("Please enter a response")
      return
    }

    setIsSubmitting(true)
    try {
      const response = await inquiryService.respondToInquiry(selectedInquiry.id, { response: replyText })

      if (response.data && response.data.enquiry) {
        toast.success("Response sent successfully")
        setIsReplyMode(false)
        setIsDialogOpen(false)

        // Refresh the inquiries list
        if (onRefresh) {
          onRefresh()
        }
      } else {
        console.error("Unexpected response structure:", response)
        toast.error("Failed to send response. Unexpected data format.")
      }
    } catch (error) {
      console.error("Error sending response:", error)
      toast.error("Failed to send response. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMM yyyy, hh:mm a')
    } catch (error) {
      return dateString
    }
  }

  return (
    <>
      {isLoading ? (
        // Loading skeleton
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 px-6 py-3">
                <div className="flex w-full items-center justify-between">
                  <Skeleton className="h-5 w-20" />
                  <Skeleton className="h-9 w-24" />
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : inquiries.length === 0 ? (
        // No inquiries found
        <div className="text-center py-10">
          <p className="text-muted-foreground">No inquiries found</p>
        </div>
      ) : (
        // Inquiries list
        <div className="space-y-4">
          {inquiries.map((inquiry) => (
            <Card key={inquiry.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {inquiry.user?.companyName?.[0] || inquiry.companyName?.[0] || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="font-semibold">
                        {inquiry.user?.companyName || inquiry.companyName}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(inquiry.createdAt)}
                      </div>
                    </div>
                    <div className="text-sm font-medium">Sub: {inquiry.subject}</div>
                    <div className="text-sm text-muted-foreground line-clamp-1">{inquiry.message}</div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 px-6 py-3">
                <div className="flex w-full items-center justify-between">
                  <Badge
                    variant={
                      inquiry.status === "PENDING"
                        ? "outline"
                        : inquiry.status === "RESPONDED"
                          ? "secondary"
                          : "default"
                    }
                  >
                    {inquiry.status === "PENDING"
                      ? "Pending"
                      : inquiry.status === "RESPONDED"
                        ? "Responded"
                        : "Closed"}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(inquiry)}
                  >
                    View Details
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {selectedInquiry && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Inquiry #{selectedInquiry.enquiryNumber || selectedInquiry.id.substring(0, 8)}</DialogTitle>
              <DialogDescription>{formatDate(selectedInquiry.createdAt)}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <Avatar className="h-10 w-10">
                  <AvatarFallback>
                    {selectedInquiry.user?.companyName?.[0] || selectedInquiry.companyName?.[0] || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-semibold">
                    {selectedInquiry.user?.companyName || selectedInquiry.companyName}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {selectedInquiry.user?.email || selectedInquiry.email}
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-semibold mb-2">Subject: {selectedInquiry.subject}</h4>
                <div className="rounded-md bg-muted p-4 text-sm whitespace-pre-line">
                  {selectedInquiry.message}
                </div>
              </div>

              {selectedInquiry.response && !isReplyMode && (
                <div>
                  <h4 className="text-sm font-semibold mb-2">Response:</h4>
                  <div className="rounded-md bg-muted p-4 text-sm whitespace-pre-line">
                    {selectedInquiry.response}
                  </div>
                  {selectedInquiry.respondedAt && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Responded on {formatDate(selectedInquiry.respondedAt)}
                    </p>
                  )}
                </div>
              )}

              {isReplyMode && (
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold">Send a Reply</h4>
                  <Textarea
                    placeholder="Type your response here..."
                    className="min-h-[150px]"
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              {!isReplyMode && selectedInquiry.status === "PENDING" ? (
                <Button onClick={handleReply}>
                  <Send className="mr-2 h-4 w-4" />
                  Send a Reply
                </Button>
              ) : isReplyMode ? (
                <div className="flex gap-2 w-full">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setIsReplyMode(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="w-full"
                    onClick={handleSendReply}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="mr-2 h-4 w-4" />
                    )}
                    Send
                  </Button>
                </div>
              ) : null}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}

"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, MoreHorizontal, Trash } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StockItem } from "@/lib/api/stockService"

interface StockCardsProps {
  data: StockItem[]
  onEdit: (stock: StockItem) => void
  onDelete: (stock: StockItem) => void
  isLoading?: boolean
}

export function StockCards({ data, onEdit, onDelete, isLoading = false }: StockCardsProps) {
  // Generate a simple SKU from the stock properties
  const generateSku = (stock: StockItem) => {
    return `KP-${stock.type}-${stock.gsm}-${stock.bf}`.toUpperCase();
  };

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <p className="text-muted-foreground">Loading stock data...</p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="flex h-40 items-center justify-center">
        <p className="text-muted-foreground">No stock items found.</p>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {data.map((item) => (
        <Card key={item.id} className="overflow-hidden">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-base">{item.type}</CardTitle>
                <CardDescription>{generateSku(item)}</CardDescription>
              </div>
              {item.rollsAvailable <= 3 ? <Badge variant="destructive">Low Stock</Badge> : null}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => onEdit(item)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => onDelete(item)}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="space-y-1">
                <p className="text-muted-foreground">Paper Type</p>
                <p className="font-medium">{item.type}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">GSM</p>
                <p className="font-medium">{item.gsm}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">BF</p>
                <p className="font-medium">{item.bf}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">Price</p>
                <p className="font-medium">₹{item.pricePerRoll.toLocaleString()} per Roll</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t bg-muted/50 px-6 py-3">
            <div className="flex justify-between items-center w-full">
              <div className="text-sm font-medium">Available Rolls</div>
              <div className="text-lg font-bold">{item.rollsAvailable}</div>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This middleware is disabled, all authentication is handled on the client side
export function middleware(request: NextRequest) {
  // Always pass through all requests
  return NextResponse.next();
}

// This config limits which paths the middleware runs on
export const config = {
  // Only run middleware on API routes, not on any other routes
  matcher: ['/api/:path*'],
};

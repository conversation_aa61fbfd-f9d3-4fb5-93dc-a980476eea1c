"use client"

import { useState, useCallback } from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Search, RefreshCw } from "lucide-react"
import { InquiriesList } from "@/components/inquiries-list"

export default function InquiriesPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [refreshKey, setRefreshKey] = useState(0)

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshKey(prev => prev + 1)
  }, [])

  return (
    <DashboardShell>
      <DashboardHeader heading="Inquiries" text="Manage customer inquiries">
        <div className="flex items-center gap-2">
          {/* <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search inquiries..."
              className="w-[200px] pl-8 md:w-[300px]"
              value={searchQuery}
              onChange={handleSearch}
            />
          </div> */}

          <Button variant="outline" size="icon" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </DashboardHeader>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Inquiries</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="responded">Responded</TabsTrigger>
          <TabsTrigger value="closed">Closed</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4">
          <InquiriesList key={`all-${refreshKey}`} status="all" onRefresh={handleRefresh} />
        </TabsContent>
        <TabsContent value="pending" className="space-y-4">
          <InquiriesList key={`pending-${refreshKey}`} status="pending" onRefresh={handleRefresh} />
        </TabsContent>
        <TabsContent value="responded" className="space-y-4">
          <InquiriesList key={`responded-${refreshKey}`} status="responded" onRefresh={handleRefresh} />
        </TabsContent>
        <TabsContent value="closed" className="space-y-4">
          <InquiriesList key={`closed-${refreshKey}`} status="closed" onRefresh={handleRefresh} />
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}

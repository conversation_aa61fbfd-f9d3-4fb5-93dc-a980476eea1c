"use client";

import { useEffect, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';

export default function TokenInjector() {
  const pathname = usePathname();
  const router = useRouter();
  const isRedirectingRef = useRef(false);
  const lastRedirectPathRef = useRef<string | null>(null);
  
  useEffect(() => {
    // Prevent execution during server-side rendering
    if (typeof window === 'undefined') return;

    // Skip if there's a manual redirect in progress from login/logout functions
    if (sessionStorage.getItem('manual_redirect') === 'true') {
      console.log('Manual redirect in progress, skipping token injector check');
      return;
    }

    // Avoid redirect loops by tracking if we're already redirecting
    if (isRedirectingRef.current) {
      console.log('Already redirecting, skipping check');
      return;
    }

    // Check if this is the path we just redirected to
    if (lastRedirectPathRef.current === pathname) {
      console.log('Already redirected to this path, skipping check');
      lastRedirectPathRef.current = null;
      return;
    }

    const token = localStorage.getItem('auth_token');
    const isPublicPath = ['/login'].some(path => pathname.startsWith(path));
    
    // Handle redirects with guards to prevent loops
    if (!isPublicPath && !token) {
      console.log('Not authenticated, redirecting to login');
      isRedirectingRef.current = true;
      lastRedirectPathRef.current = '/login';
      
      setTimeout(() => {
        router.replace('/login');
        // Reset the flag after redirect
        setTimeout(() => {
          isRedirectingRef.current = false;
        }, 100);
      }, 0);
    } else if (pathname === '/login' && token) {
      console.log('Already authenticated, redirecting to dashboard');
      isRedirectingRef.current = true;
      lastRedirectPathRef.current = '/dashboard';
      
      setTimeout(() => {
        router.replace('/dashboard');
        // Reset the flag after redirect
        setTimeout(() => {
          isRedirectingRef.current = false;
        }, 100);
      }, 0);
    }
  }, [pathname, router]);
  
  return null;
}

"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import stockService, { type StockItem, type CreateStockItemDto, type UpdateStockItemDto } from "@/lib/api/stockService"

// Paper type options
const PAPER_TYPES = ["NS", "GY"]

// GSM options
const GSM_OPTIONS = [80, 100, 120, 140, 180, 200, 230, 250]

// BF options
const BF_OPTIONS = [16, 18, 20, 22, 25, 28]

// Form schema
const stockFormSchema = z.object({
  type: z.string().min(1, "Type is required"),
  gsm: z.coerce.number().min(1, "GSM must be a positive number"),
  bf: z.coerce.number().min(1, "BF must be a positive number"),
  rollsAvailable: z.coerce.number().min(0, "Rolls available must be a non-negative number"),
  pricePerRoll: z.coerce.number().min(1, "Price per roll must be a positive number"),
})

type StockFormValues = z.infer<typeof stockFormSchema>

interface StockFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  stock?: StockItem
  onSuccess: () => void
}

export function StockFormDialog({ open, onOpenChange, stock, onSuccess }: StockFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = !!stock

  console.log("Is user Editing " , isEditing)

  // Initialize form
  const form = useForm<StockFormValues>({
    resolver: zodResolver(stockFormSchema),
    defaultValues: {
      type: stock?.type || "",
      gsm: stock?.gsm || 0,
      bf: stock?.bf || 0,
      rollsAvailable: stock?.rollsAvailable || 0,
      pricePerRoll: stock?.pricePerRoll || 0,
    },
  })

  // Handle form submission
  const onSubmit = async (values: StockFormValues) => {
    setIsSubmitting(true)
    try {
      if (isEditing && stock) {
        // Update existing stock
        await stockService.updateStockItem(stock.id, values as UpdateStockItemDto)
        toast.success("Stock updated successfully")
      } else {
        // Create new stock
        await stockService.createStockItem(values as CreateStockItemDto)
        toast.success("Stock added successfully")
      }
      onOpenChange(false)
      onSuccess()
      form.reset()
    } catch (error) {
      console.error("Error submitting stock form:", error)
      toast.error(`Failed to ${isEditing ? "update" : "add"} stock. Please try again.`)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Stock" : "Add New Stock"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the details of the existing stock item."
              : "Add a new stock item to the inventory."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paper Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select paper type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {PAPER_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gsm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GSM</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    defaultValue={field.value ? field.value.toString() : undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select GSM" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {GSM_OPTIONS.map((gsm) => (
                        <SelectItem key={gsm} value={gsm.toString()}>
                          {gsm}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bf"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bursting Factor (BF)</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    defaultValue={field.value ? field.value.toString() : undefined}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select BF" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {BF_OPTIONS.map((bf) => (
                        <SelectItem key={bf} value={bf.toString()}>
                          {bf}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="rollsAvailable"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Available Rolls</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 50" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="pricePerRoll"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price per Roll (₹)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="e.g., 5000" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : isEditing ? "Update" : "Add"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { StockQueryParams } from "@/lib/api/stockService"

// Paper type options
const PAPER_TYPES = ["NS", "GY"]

// GSM options
const GSM_OPTIONS = [80, 100, 120, 140, 180, 200, 230, 250]

// BF options
const BF_OPTIONS = [16, 18, 20, 22, 25, 28]

// Form schema
const filterFormSchema = z.object({
  type: z.string().optional(),
  gsm: z.coerce.number().optional(),
  bf: z.coerce.number().optional(),
})

type FilterFormValues = z.infer<typeof filterFormSchema>

interface StockFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentFilters: StockQueryParams
  onApplyFilters: (filters: StockQueryParams) => void
}

export function StockFilterDialog({
  open,
  onOpenChange,
  currentFilters,
  onApplyFilters,
}: StockFilterDialogProps) {
  // Initialize form with current filters
  const form = useForm<FilterFormValues>({
    resolver: zodResolver(filterFormSchema),
    defaultValues: {
      type: currentFilters.type || "all",
      gsm: currentFilters.gsm || undefined,
      bf: currentFilters.bf || undefined,
    },
  })

  // Handle form submission
  const onSubmit = (values: FilterFormValues) => {
    // Remove empty string values and undefined values
    const filters: StockQueryParams = { ...currentFilters };

    // Update filters with form values
    if (values.type && values.type !== 'all') {
      filters.type = values.type;
    } else {
      delete filters.type;
    }

    if (values.gsm && values.gsm !== undefined) {
      filters.gsm = values.gsm;
    } else {
      delete filters.gsm;
    }

    if (values.bf && values.bf !== undefined) {
      filters.bf = values.bf;
    } else {
      delete filters.bf;
    }

    // Reset to page 1 when applying new filters
    filters.page = 1;

    onApplyFilters(filters);
    onOpenChange(false);
  }

  // Handle reset filters
  const handleReset = () => {
    form.reset({
      type: "all",
      gsm: undefined,
      bf: undefined,
    });

    // Apply reset (only keep pagination)
    onApplyFilters({
      page: 1,
      limit: currentFilters.limit,
    });

    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Filter Stock</DialogTitle>
          <DialogDescription>
            Filter stock items by type, GSM, and bursting factor.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paper Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select paper type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {PAPER_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gsm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GSM</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value === "all" ? undefined : parseInt(value))}
                    defaultValue={field.value ? field.value.toString() : "all"}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select GSM" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All GSM</SelectItem>
                      {GSM_OPTIONS.map((gsm) => (
                        <SelectItem key={gsm} value={gsm.toString()}>
                          {gsm}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bf"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bursting Factor (BF)</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value === "all" ? undefined : parseInt(value))}
                    defaultValue={field.value ? field.value.toString() : "all"}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select BF" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All BF</SelectItem>
                      {BF_OPTIONS.map((bf) => (
                        <SelectItem key={bf} value={bf.toString()}>
                          {bf}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter className="gap-2 sm:gap-0">
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset Filters
              </Button>
              <Button type="submit">Apply Filters</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

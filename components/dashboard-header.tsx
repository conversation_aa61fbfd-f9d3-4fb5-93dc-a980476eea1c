import type React from "react"
import { NotificationDropdown } from "@/components/notifications/notification-dropdown"
import { ThemeToggle } from "@/components/theme-toggle"

interface DashboardHeaderProps {
  heading: string
  text?: string
  children?: React.ReactNode
}

export function DashboardHeader({ heading, text, children }: DashboardHeaderProps) {
  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 px-2">
      <div className="grid gap-1">
        <h1 className="text-2xl font-bold tracking-wide">{heading}</h1>
        {text && <p className="text-muted-foreground">{text}</p>}
      </div>
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-2 md:space-x-4">
          <NotificationDropdown />
          <ThemeToggle />
        </div>
        <div className="hidden md:flex">{children}</div>
      </div>
      <div className="flex md:hidden mt-4">{children}</div>
    </div>
  )
}

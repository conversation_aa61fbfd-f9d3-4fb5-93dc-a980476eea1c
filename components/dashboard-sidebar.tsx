"use client"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  BarChart3,
  Box,
  LayoutDashboard,
  LogOut,
  MessageSquare,
  Moon,
  Package,
  PanelRight,
  Search,
  Settings,
  ShoppingCart,
  Sun,
  Users,
} from "lucide-react"
import { useAuth } from "@/lib/context/auth-context"
import { useTheme } from "next-themes"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { useState } from "react"

// This component will always be visible at the edge of the screen
function FloatingTriggerButton() {
  const { toggleSidebar, state } = useSidebar()

  return (
    <Button
      onClick={toggleSidebar}
      variant="outline"
      size="icon"
      className={`fixed left-0 top-4 z-50 h-8 w-8 rounded-r-lg rounded-l-none border-l-0 bg-background shadow-md transition-all duration-200 ${state === "expanded" ? "opacity-0" : "opacity-100"}`}
    >
      <PanelRight className="h-4 w-4" />
      <span className="sr-only">Open sidebar</span>
    </Button>
  )
}

export function DashboardSidebar() {
  const pathname = usePathname()
  const router = useRouter()
  const { user, logout } = useAuth()
  const { theme, setTheme } = useTheme()
  const [searchQuery, setSearchQuery] = useState("")

  const routes = [
    {
      title: "Dashboard",
      icon: LayoutDashboard,
      href: "/dashboard",
      variant: "default",
    },
    {
      title: "Analytics",
      icon: BarChart3,
      href: "/analytics",
      variant: "default",
    },
    {
      title: "Stock",
      icon: Package,
      href: "/stock",
      variant: "default",
    },
    {
      title: "Orders",
      icon: ShoppingCart,
      href: "/orders",
      variant: "default",
    },
    {
      title: "Inquiries",
      icon: MessageSquare,
      href: "/inquiries",
      variant: "default",
    },
    {
      title: "Users",
      icon: Users,
      href: "/users",
      variant: "default",
    },
  ]

  // Filter routes based on search query
  const filteredRoutes = searchQuery
    ? routes.filter((route) => route.title.toLowerCase().includes(searchQuery.toLowerCase()))
    : routes

  return (
    <>
      <FloatingTriggerButton />
      <Sidebar>
        <SidebarHeader className="flex flex-col items-start gap-2 px-4 py-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <Box className="h-6 w-6" />
              <span className="text-lg font-semibold">Kraft Admin</span>
            </div>
            <SidebarTrigger className="h-7 w-7" />
          </div>
          <div className="relative w-full mt-2">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search menu..."
              className="w-full pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarMenu>
            {filteredRoutes.map((route) => (
              <SidebarMenuItem key={route.href}>
                <SidebarMenuButton asChild isActive={pathname === route.href} tooltip={route.title}>
                  <Link href={route.href}>
                    <route.icon className="h-5 w-5" />
                    <span>{route.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarContent>
        <SidebarFooter className="border-t p-4">
          <div className="flex flex-col space-y-4">
            <Button
              variant="outline"
              size="sm"
              className="justify-start"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              {theme === "dark" ? (
                <>
                  <Sun className="mr-2 h-4 w-4" />
                  <span>Light Mode</span>
                </>
              ) : (
                <>
                  <Moon className="mr-2 h-4 w-4" />
                  <span>Dark Mode</span>
                </>
              )}
            </Button>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder-user.jpg" alt="Admin" />
                  <AvatarFallback>AD</AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{user?.contactPerson || "Admin User"}</p>
                  <p className="text-xs text-muted-foreground">{user?.email || "<EMAIL>"}</p>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Settings className="h-4 w-4" />
                    <span className="sr-only">Settings</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => logout()}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </SidebarFooter>
      </Sidebar>
    </>
  )
}
